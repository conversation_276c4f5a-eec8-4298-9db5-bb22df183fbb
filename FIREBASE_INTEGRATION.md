# Firebase Firestore Integration for Habit Tracker

This document explains the complete Firebase Firestore integration for the habit tracking app, following clean architecture principles.

## 📦 Firestore Schema

```
users (Collection)
└── [userId] (Document)
    ├── habits (Collection)
    │   └── [habitId] (Document)
    │       ├── name: String (e.g., "Fajr Prayer")
    │       ├── points: Double (e.g., 1.0)
    │       └── createdAt: Timestamp
    │
    └── days (Collection)
        └── [yyyy-MM-dd] (Document)
            ├── date: Timestamp
            ├── completedHabits: List<String> ← (list of habitId)
            ├── manualEvaluation: Double (0 to 3)
            ├── habitSnapshot: List<Map<String, dynamic>> ←
            │   [
            │     { name: "Fajr Prayer", points: 1.0, id: "abc123" },
            │     { name: "Rout<PERSON>", points: 1.0, id: "xyz456" }
            │   ]
            └── calculatedScore: Double ← Optional
```

## 🏗️ Architecture

### Domain Layer
- **Entities**: `Habit`, `Day`, `HabitSnapshot`
- **Repositories**: Abstract interfaces for data operations

### Data Layer
- **DataSources**: Firebase-specific implementations
- **Repositories**: Concrete implementations of domain repositories
- **Services**: High-level business logic services

### Key Files

```
lib/
├── domain/
│   ├── entities/
│   │   ├── habit.dart          # Habit entity with Firestore mapping
│   │   └── day.dart            # Day entity with score calculation
│   └── repositories/
│       ├── habit_repository.dart
│       └── daily_log_repository.dart
├── data/
│   ├── datasources/
│   │   ├── firebase_habit_datasource.dart    # Habit CRUD operations
│   │   └── firebase_day_datasource.dart      # Day CRUD operations
│   ├── repositories/
│   │   ├── firebase_habit_repository.dart
│   │   └── firebase_day_repository.dart
│   └── services/
│       └── firebase_habit_service.dart       # Complete workflow service
└── examples/
    └── firebase_example.dart                 # Usage examples
```

## 🚀 Usage Examples

### 1. Creating a Habit

```dart
final habitDataSource = FirebaseHabitDataSource();
const userId = 'user123';

final habit = Habit(
  id: '', // Generated by Firestore
  name: 'Fajr Prayer',
  points: 1.0,
  createdAt: DateTime.now(),
);

final habitId = await habitDataSource.addHabit(userId, habit);
```

### 2. Recording a Day

```dart
final dayDataSource = FirebaseDayDataSource();
final habitService = FirebaseHabitService(habitDataSource, dayDataSource);

// Record today with completed habits
await habitService.recordDay(
  userId: userId,
  date: DateTime.now(),
  completedHabitIds: ['habit1', 'habit2'],
  manualEvaluation: 2.5,
);
```

### 3. Marking Habit as Completed

```dart
await habitService.markHabitCompleted(
  userId: userId,
  habitId: 'habit1',
  date: DateTime.now(),
);
```

## 🧮 Score Calculation

The score is calculated using this formula:

```
Score = (Total completed habit points + Manual evaluation) ÷ (Total possible habit points + 3)
```

### Example Calculation

**Habits:**
- Fajr Prayer: 1.0 points ✅
- Morning Routine: 1.0 points ✅  
- Exercise: 2.0 points ❌

**Manual Evaluation:** 2.5/3.0

**Calculation:**
```
Completed Points: 1.0 + 1.0 = 2.0
Total Possible: 1.0 + 1.0 + 2.0 = 4.0
Score = (2.0 + 2.5) ÷ (4.0 + 3.0) = 4.5 ÷ 7.0 = 64.3%
```

## 🔧 Implementation Details

### Habit Snapshots

When a day is created, it captures a snapshot of all current habits. This ensures:
- Historical accuracy (habits can be modified without affecting past days)
- Consistent scoring (points remain the same for past days)
- Data integrity (deleted habits don't break past records)

### Atomic Operations

Day updates use Firestore transactions to ensure:
- Consistent state during concurrent updates
- Automatic score recalculation
- Data integrity

### Date Formatting

Days use `yyyy-MM-dd` format as document IDs:
- Easy querying by date range
- Consistent sorting
- Human-readable document names

## 🛠️ Setup Instructions

1. **Add Firebase to your Flutter project:**
   ```bash
   flutter pub add firebase_core cloud_firestore firebase_auth
   ```

2. **Configure Firebase:**
   - Create a Firebase project
   - Add your Flutter app
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)

3. **Initialize Firebase:**
   ```dart
   await Firebase.initializeApp();
   ```

4. **Use the services:**
   ```dart
   final habitService = FirebaseHabitService(
     FirebaseHabitDataSource(),
     FirebaseDayDataSource(),
   );
   
   await habitService.demonstrateWorkflow('user123');
   ```

## 📊 Data Flow

1. **Create Habits** → Store in `users/{userId}/habits/{habitId}`
2. **Start Day** → Create snapshot in `users/{userId}/days/{yyyy-MM-dd}`
3. **Track Progress** → Update `completedHabits` array
4. **Calculate Score** → Use formula with snapshots and manual evaluation
5. **Store Result** → Save `calculatedScore` in day document

## 🔒 Security Rules

Recommended Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      match /habits/{habitId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /days/{dayId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
  }
}
```

## 🧪 Testing

Run the examples to test the integration:

```dart
// Complete workflow demonstration
await FirebaseExample.runExample();

// Manual step-by-step example
await FirebaseExample.manualExample();

// Score calculation examples
FirebaseExample.scoreCalculationExample();
```

This implementation provides a robust, scalable foundation for habit tracking with Firebase Firestore while maintaining clean architecture principles.
