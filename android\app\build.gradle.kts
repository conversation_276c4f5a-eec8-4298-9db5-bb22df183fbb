plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

android {
    namespace = "com.example.adaa"
    compileSdk = 35 // Required by dependencies
    ndkVersion = "27.0.12077973" // Required by Firebase plugins

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.adaa"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21 // Minimum SDK version for Firebase
        targetSdk = 35 // Target SDK version for plugin compatibility
        versionCode = 1
        versionName = "1.0.0"
        multiDexEnabled = true
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Temporarily disabled Firebase
    // Add the Firebase BoM
    implementation(platform("com.google.firebase:firebase-bom:32.6.0"))

    // Add Firebase Analytics
    implementation("com.google.firebase:firebase-analytics")

    // Add Firebase Auth
    implementation("com.google.firebase:firebase-auth")

    // Add Firebase Firestore
    implementation("com.google.firebase:firebase-firestore")

    // Add multidex support
    implementation("androidx.multidex:multidex:2.0.1")
}
