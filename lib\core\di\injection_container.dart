import 'package:adaa/data/datasources/firebase_day_datasource.dart';
import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/data/repositories/firebase_day_repository.dart';
import 'package:adaa/data/repositories/firebase_habit_repository.dart';
import 'package:adaa/data/services/firebase_habit_service.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';
import 'package:adaa/domain/repositories/habit_repository.dart';
import 'package:adaa/domain/usecases/daily_log_usecases.dart';
import 'package:adaa/domain/usecases/habit_usecases.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Firebase services
  sl.registerLazySingleton<FirebaseAuth>(() => FirebaseAuth.instance);
  sl.registerLazySingleton<FirebaseFirestore>(() => FirebaseFirestore.instance);

  // Data sources
  sl.registerLazySingleton(() => FirebaseHabitDataSource());
  sl.registerLazySingleton(() => FirebaseDayDataSource());

  // Repositories
  sl.registerLazySingleton<HabitRepository>(
    () => FirebaseHabitRepository(sl()),
  );
  sl.registerLazySingleton<DailyLogRepository>(
    () => FirebaseDayRepository(sl(), sl()),
  );

  // Services
  sl.registerLazySingleton(() => FirebaseHabitService(sl(), sl()));

  // Use cases - Habits
  sl.registerLazySingleton(() => GetHabitsUseCase(sl()));
  sl.registerLazySingleton(() => GetActiveHabitsUseCase(sl()));
  sl.registerLazySingleton(() => GetHabitByIdUseCase(sl()));
  sl.registerLazySingleton(() => AddHabitUseCase(sl()));
  sl.registerLazySingleton(() => UpdateHabitUseCase(sl()));
  sl.registerLazySingleton(() => DeleteHabitUseCase(sl()));

  // Use cases - Daily Logs
  sl.registerLazySingleton(() => GetDailyLogUseCase(sl()));
  sl.registerLazySingleton(() => GetDailyLogsForRangeUseCase(sl()));
  sl.registerLazySingleton(() => SaveDailyLogUseCase(sl()));
  sl.registerLazySingleton(() => MarkHabitAsCompletedUseCase(sl()));
  sl.registerLazySingleton(() => MarkHabitAsNotCompletedUseCase(sl()));
  sl.registerLazySingleton(() => UpdateManualScoreUseCase(sl()));
}
