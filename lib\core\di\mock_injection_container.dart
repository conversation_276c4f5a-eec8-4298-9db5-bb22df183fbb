import 'package:adaa/core/services/mock_services.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';
import 'package:adaa/domain/repositories/habit_repository.dart';
import 'package:adaa/domain/usecases/daily_log_usecases.dart';
import 'package:adaa/domain/usecases/habit_usecases.dart';
import 'package:adaa/presentation/blocs/auth/auth_cubit.dart' as auth;
import 'package:get_it/get_it.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Mock services
  sl.registerLazySingleton(() => auth.MockFirebaseAuth());
  sl.registerLazySingleton(() => MockFirestore());

  // Mock repositories
  sl.registerLazySingleton<HabitRepository>(() => MockHabitRepository());
  sl.registerLazySingleton<DailyLogRepository>(() => MockDailyLogRepository());

  // Use cases - Habits
  sl.registerLazySingleton(() => GetHabitsUseCase(sl()));
  sl.registerLazySingleton(() => GetActiveHabitsUseCase(sl()));
  sl.registerLazySingleton(() => GetHabitByIdUseCase(sl()));
  sl.registerLazySingleton(() => AddHabitUseCase(sl()));
  sl.registerLazySingleton(() => UpdateHabitUseCase(sl()));
  sl.registerLazySingleton(() => DeleteHabitUseCase(sl()));

  // Use cases - Daily Logs
  sl.registerLazySingleton(() => GetDailyLogUseCase(sl()));
  sl.registerLazySingleton(() => GetDailyLogsForRangeUseCase(sl()));
  sl.registerLazySingleton(() => SaveDailyLogUseCase(sl()));
  sl.registerLazySingleton(() => MarkHabitAsCompletedUseCase(sl()));
  sl.registerLazySingleton(() => MarkHabitAsNotCompletedUseCase(sl()));
  sl.registerLazySingleton(() => UpdateManualScoreUseCase(sl()));
}
