import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Firebase configuration and initialization
class FirebaseConfig {
  static bool _initialized = false;

  /// Initialize Firebase
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      await Firebase.initializeApp();
      _initialized = true;
      print('✅ Firebase initialized successfully');
    } catch (e) {
      print('❌ Firebase initialization failed: $e');
      rethrow;
    }
  }

  /// Get current user ID (for demo purposes, returns a mock user)
  static String getCurrentUserId() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      return user.uid;
    }
    
    // For demo purposes, return a mock user ID
    // In a real app, you would handle authentication properly
    return 'demo-user-${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Check if Firebase is initialized
  static bool get isInitialized => _initialized;

  /// Get Firestore instance
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;

  /// Get Auth instance
  static FirebaseAuth get auth => FirebaseAuth.instance;
}
