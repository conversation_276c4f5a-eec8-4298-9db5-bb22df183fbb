import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';
import 'package:adaa/domain/repositories/habit_repository.dart';
import 'package:flutter/foundation.dart';

// Mock Firebase Auth
class MockFirebaseAuth {
  final String uid = 'mock-user-123';
  bool isSignedIn = false;

  Future<void> signInAnonymously() async {
    await Future.delayed(const Duration(milliseconds: 500));
    isSignedIn = true;
    debugPrint('Mock user signed in anonymously');
  }

  Future<void> signOut() async {
    await Future.delayed(const Duration(milliseconds: 300));
    isSignedIn = false;
    debugPrint('Mock user signed out');
  }

  Stream<bool> authStateChanges() {
    return Stream.value(isSignedIn);
  }
}

// Mock Firestore
class MockFirestore {
  final Map<String, Map<String, dynamic>> _collections = {};

  MockCollection collection(String path) {
    if (!_collections.containsKey(path)) {
      _collections[path] = {};
    }
    return MockCollection(this, path);
  }
}

class MockCollection {
  final MockFirestore _firestore;
  final String path;

  MockCollection(this._firestore, this.path);

  MockDocument doc(String id) {
    return MockDocument(_firestore, '$path/$id');
  }
}

class MockDocument {
  final MockFirestore _firestore;
  final String path;

  MockDocument(this._firestore, this.path);

  Future<void> set(Map<String, dynamic> data) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final parts = path.split('/');
    final collection = parts[0];
    final id = parts[1];

    if (!_firestore._collections.containsKey(collection)) {
      _firestore._collections[collection] = {};
    }

    _firestore._collections[collection]![id] = data;
    debugPrint('Mock document set: $path with data: $data');
  }

  Future<void> update(Map<String, dynamic> data) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final parts = path.split('/');
    final collection = parts[0];
    final id = parts[1];

    if (_firestore._collections.containsKey(collection) &&
        _firestore._collections[collection]!.containsKey(id)) {
      _firestore._collections[collection]![id]!.addAll(data);
      debugPrint('Mock document updated: $path with data: $data');
    }
  }

  Future<void> delete() async {
    await Future.delayed(const Duration(milliseconds: 300));
    final parts = path.split('/');
    final collection = parts[0];
    final id = parts[1];

    if (_firestore._collections.containsKey(collection) &&
        _firestore._collections[collection]!.containsKey(id)) {
      _firestore._collections[collection]!.remove(id);
      debugPrint('Mock document deleted: $path');
    }
  }
}

// Mock Repositories
class MockHabitRepository implements HabitRepository {
  final List<Habit> _habits = [
    Habit(
      id: 'habit-1',
      name: 'Morning Meditation',
      points: 1.0,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    Habit(
      id: 'habit-2',
      name: 'Read a Book',
      points: 1.0,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
    Habit(
      id: 'habit-3',
      name: 'Exercise',
      points: 2.0,
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
    ),
  ];

  @override
  Stream<List<Habit>> getHabits(String userId) async* {
    await Future.delayed(const Duration(milliseconds: 500));
    yield _habits; // Return all habits for simplicity in mock
  }

  @override
  Stream<List<Habit>> getActiveHabits(String userId) async* {
    await Future.delayed(const Duration(milliseconds: 500));
    yield _habits; // Return all habits for simplicity in mock
  }

  @override
  Future<Habit?> getHabitById(String userId, String habitId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    try {
      return _habits.firstWhere((habit) => habit.id == habitId);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String> addHabit(String userId, Habit habit) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final newHabit = habit.copyWith(
      id: 'habit-${DateTime.now().millisecondsSinceEpoch}',
    );
    _habits.add(newHabit);
    return newHabit.id;
  }

  @override
  Future<void> updateHabit(String userId, Habit habit) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final index = _habits.indexWhere((h) => h.id == habit.id);
    if (index != -1) {
      _habits[index] = habit;
    }
  }

  @override
  Future<void> deleteHabit(String userId, String habitId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _habits.removeWhere((habit) => habit.id == habitId);
  }
}

class MockDailyLogRepository implements DailyLogRepository {
  final Map<String, Day> _days = {};

  MockDailyLogRepository() {
    // Initialize with today's log
    final today = DateTime.now();
    final todayDay = Day(
      id: Day.formatDateId(today),
      date: today,
      completedHabits: ['habit-1', 'habit-3'],
      manualEvaluation: 2.5,
      habitSnapshot: [
        HabitSnapshot(id: 'habit-1', name: 'Morning Meditation', points: 1.0),
        HabitSnapshot(id: 'habit-2', name: 'Read a Book', points: 1.0),
        HabitSnapshot(id: 'habit-3', name: 'Exercise', points: 2.0),
      ],
    );
    _days[todayDay.id] = todayDay;
  }

  @override
  Future<Day?> getDailyLog(String userId, DateTime date) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final dateId = Day.formatDateId(date);
    return _days[dateId];
  }

  @override
  Stream<List<Day>> getDailyLogsForRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async* {
    await Future.delayed(const Duration(milliseconds: 500));
    final startDateId = Day.formatDateId(startDate);
    final endDateId = Day.formatDateId(endDate);

    final filteredDays =
        _days.values
            .where(
              (day) =>
                  day.id.compareTo(startDateId) >= 0 &&
                  day.id.compareTo(endDateId) <= 0,
            )
            .toList();

    yield filteredDays;
  }

  @override
  Future<void> saveDailyLog(String userId, Day day) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _days[day.id] = day;
  }

  @override
  Future<void> markHabitAsCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final day = await getDailyLog(userId, date);
    if (day != null) {
      final updatedCompletedHabits = List<String>.from(day.completedHabits);
      if (!updatedCompletedHabits.contains(habitId)) {
        updatedCompletedHabits.add(habitId);
      }

      final updatedDay = day.copyWith(completedHabits: updatedCompletedHabits);

      await saveDailyLog(userId, updatedDay);
    }
  }

  @override
  Future<void> markHabitAsNotCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final day = await getDailyLog(userId, date);
    if (day != null) {
      final updatedCompletedHabits = List<String>.from(day.completedHabits);
      updatedCompletedHabits.remove(habitId);

      final updatedDay = day.copyWith(completedHabits: updatedCompletedHabits);

      await saveDailyLog(userId, updatedDay);
    }
  }

  @override
  Future<void> updateManualScore(
    String userId,
    DateTime date,
    double score,
  ) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final day = await getDailyLog(userId, date);
    if (day != null) {
      final updatedDay = day.copyWith(manualEvaluation: score);

      await saveDailyLog(userId, updatedDay);
    }
  }
}
