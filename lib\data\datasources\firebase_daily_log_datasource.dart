import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:adaa/data/models/daily_log_model.dart';
import 'package:uuid/uuid.dart';

class FirebaseDailyLogDataSource {
  final FirebaseFirestore _firestore;
  final _uuid = const Uuid();

  FirebaseDailyLogDataSource({FirebaseFirestore? firestore})
    : _firestore = firestore ?? FirebaseFirestore.instance;

  // Collection reference
  CollectionReference get _dailyLogsCollection =>
      _firestore.collection('dailyLogs');

  // Format date to string (YYYY-MM-DD)
  String _formatDate(DateTime date) {
    return date.toIso8601String().split('T')[0];
  }

  // Get daily log for a specific date
  Future<DayModel?> getDailyLog(String userId, DateTime date) async {
    final formattedDate = _formatDate(date);

    final querySnapshot =
        await _dailyLogsCollection
            .where('userId', isEqualTo: userId)
            .where('date', isEqualTo: formattedDate)
            .limit(1)
            .get();

    if (querySnapshot.docs.isNotEmpty) {
      return DayModel.fromJson(
        querySnapshot.docs.first.data() as Map<String, dynamic>,
      );
    }
    return null;
  }

  // Get daily logs for a date range
  Future<List<DayModel>> getDailyLogsForRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final formattedStartDate = _formatDate(startDate);
    final formattedEndDate = _formatDate(endDate);

    final querySnapshot =
        await _dailyLogsCollection
            .where('userId', isEqualTo: userId)
            .where('date', isGreaterThanOrEqualTo: formattedStartDate)
            .where('date', isLessThanOrEqualTo: formattedEndDate)
            .get();

    return querySnapshot.docs
        .map((doc) => DayModel.fromJson(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Save or update a daily log
  Future<void> saveDailyLog(DayModel dailyLog) async {
    final String id = dailyLog.id.isEmpty ? _uuid.v4() : dailyLog.id;
    final logWithId = dailyLog.copyWith(id: id);

    await _dailyLogsCollection.doc(id).set(logWithId.toJson());
  }

  // Helper method to get or create a daily log
  Future<DayModel> _getOrCreateDailyLog(
    String userId,
    DateTime date,
    double maxPossibleScore,
  ) async {
    final existingLog = await getDailyLog(userId, date);

    if (existingLog != null) {
      return existingLog;
    }

    // Create a new log if none exists
    return DayModel(
      id: '',
      date: date,
      completedHabits: [],
      manualEvaluation: 0.0,
      habitSnapshot: [],
    );
  }

  // Mark a habit as completed for a specific date
  Future<void> markHabitAsCompleted(
    String userId,
    String habitId,
    DateTime date,
    double habitScore,
    double maxPossibleScore,
  ) async {
    final dailyLog = await _getOrCreateDailyLog(userId, date, maxPossibleScore);

    if (!dailyLog.completedHabits.contains(habitId)) {
      final updatedCompletedHabits = List<String>.from(dailyLog.completedHabits)
        ..add(habitId);

      final updatedLog = dailyLog.copyWith(
        completedHabits: updatedCompletedHabits,
      );

      await saveDailyLog(updatedLog);
    }
  }

  // Mark a habit as not completed for a specific date
  Future<void> markHabitAsNotCompleted(
    String userId,
    String habitId,
    DateTime date,
    double habitScore,
    double maxPossibleScore,
  ) async {
    final dailyLog = await _getOrCreateDailyLog(userId, date, maxPossibleScore);

    if (dailyLog.completedHabits.contains(habitId)) {
      final updatedCompletedHabits = List<String>.from(dailyLog.completedHabits)
        ..remove(habitId);

      final updatedLog = dailyLog.copyWith(
        completedHabits: updatedCompletedHabits,
      );

      await saveDailyLog(updatedLog);
    }
  }

  // Update manual score for a specific date
  Future<void> updateManualScore(
    String userId,
    DateTime date,
    double manualScore,
    double maxPossibleScore,
  ) async {
    final dailyLog = await _getOrCreateDailyLog(userId, date, maxPossibleScore);

    final updatedLog = dailyLog.copyWith(manualEvaluation: manualScore);

    await saveDailyLog(updatedLog);
  }
}
