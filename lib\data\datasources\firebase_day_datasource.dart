import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/habit.dart';

/// Firebase data source for day operations
/// Follows the schema: users/{userId}/days/{yyyy-MM-dd}
class FirebaseDayDataSource {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get a specific day by date
  Future<Day?> getDayByDate(String userId, DateTime date) async {
    try {
      final dateId = Day.formatDateId(date);
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(dateId)
          .get();

      if (doc.exists) {
        return Day.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get day: $e');
    }
  }

  /// Get days within a date range
  Stream<List<Day>> getDaysInRange(String userId, DateTime startDate, DateTime endDate) {
    final startDateId = Day.formatDateId(startDate);
    final endDateId = Day.formatDateId(endDate);

    return _firestore
        .collection('users')
        .doc(userId)
        .collection('days')
        .where(FieldPath.documentId, isGreaterThanOrEqualTo: startDateId)
        .where(FieldPath.documentId, isLessThanOrEqualTo: endDateId)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Day.fromMap(doc.data(), doc.id))
            .toList());
  }

  /// Save or update a day
  Future<void> saveDay(String userId, Day day) async {
    try {
      final dateId = Day.formatDateId(day.date);
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(dateId)
          .set(day.toMap(), SetOptions(merge: true));
    } catch (e) {
      throw Exception('Failed to save day: $e');
    }
  }

  /// Create a new day with habit snapshot
  Future<void> createDayWithSnapshot(
    String userId,
    DateTime date,
    List<Habit> activeHabits,
    {List<String> completedHabits = const [],
    double manualEvaluation = 0.0}
  ) async {
    try {
      // Create habit snapshots from active habits
      final habitSnapshots = activeHabits.map((habit) => HabitSnapshot(
        id: habit.id,
        name: habit.name,
        points: habit.points,
      )).toList();

      // Create the day
      final day = Day(
        id: Day.formatDateId(date),
        date: date,
        completedHabits: completedHabits,
        manualEvaluation: manualEvaluation,
        habitSnapshot: habitSnapshots,
      );

      await saveDay(userId, day);
    } catch (e) {
      throw Exception('Failed to create day with snapshot: $e');
    }
  }

  /// Mark a habit as completed for a specific day
  Future<void> markHabitAsCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    try {
      final dateId = Day.formatDateId(date);
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(dateId);

      await _firestore.runTransaction((transaction) async {
        final doc = await transaction.get(docRef);
        
        if (doc.exists) {
          final day = Day.fromMap(doc.data()!, doc.id);
          final updatedCompletedHabits = List<String>.from(day.completedHabits);
          
          if (!updatedCompletedHabits.contains(habitId)) {
            updatedCompletedHabits.add(habitId);
            
            final updatedDay = day.copyWith(
              completedHabits: updatedCompletedHabits,
              calculatedScore: null, // Will be recalculated
            );
            
            transaction.update(docRef, updatedDay.toMap());
          }
        }
      });
    } catch (e) {
      throw Exception('Failed to mark habit as completed: $e');
    }
  }

  /// Mark a habit as not completed for a specific day
  Future<void> markHabitAsNotCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    try {
      final dateId = Day.formatDateId(date);
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(dateId);

      await _firestore.runTransaction((transaction) async {
        final doc = await transaction.get(docRef);
        
        if (doc.exists) {
          final day = Day.fromMap(doc.data()!, doc.id);
          final updatedCompletedHabits = List<String>.from(day.completedHabits);
          
          updatedCompletedHabits.remove(habitId);
          
          final updatedDay = day.copyWith(
            completedHabits: updatedCompletedHabits,
            calculatedScore: null, // Will be recalculated
          );
          
          transaction.update(docRef, updatedDay.toMap());
        }
      });
    } catch (e) {
      throw Exception('Failed to mark habit as not completed: $e');
    }
  }

  /// Update manual evaluation for a day
  Future<void> updateManualEvaluation(
    String userId,
    DateTime date,
    double manualEvaluation,
  ) async {
    try {
      final dateId = Day.formatDateId(date);
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(dateId);

      await _firestore.runTransaction((transaction) async {
        final doc = await transaction.get(docRef);
        
        if (doc.exists) {
          final day = Day.fromMap(doc.data()!, doc.id);
          final updatedDay = day.copyWith(
            manualEvaluation: manualEvaluation,
            calculatedScore: null, // Will be recalculated
          );
          
          transaction.update(docRef, updatedDay.toMap());
        }
      });
    } catch (e) {
      throw Exception('Failed to update manual evaluation: $e');
    }
  }

  /// Delete a day
  Future<void> deleteDay(String userId, DateTime date) async {
    try {
      final dateId = Day.formatDateId(date);
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('days')
          .doc(dateId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete day: $e');
    }
  }
}
