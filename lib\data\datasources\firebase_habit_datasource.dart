import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:adaa/domain/entities/habit.dart';

/// Firebase data source for habit operations
/// Follows the schema: users/{userId}/habits/{habitId}
class FirebaseHabitDataSource {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get all habits for a user
  Stream<List<Habit>> getHabits(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('habits')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => Habit.fromMap(doc.data(), doc.id))
                  .toList(),
        );
  }

  /// Get a specific habit by ID
  Future<Habit?> getHabitById(String userId, String habitId) async {
    try {
      final doc =
          await _firestore
              .collection('users')
              .doc(userId)
              .collection('habits')
              .doc(habitId)
              .get();

      if (doc.exists) {
        return Habit.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get habit: $e');
    }
  }

  /// Add a new habit
  Future<String> addHabit(String userId, Habit habit) async {
    try {
      final docRef = await _firestore
          .collection('users')
          .doc(userId)
          .collection('habits')
          .add(habit.toMap());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add habit: $e');
    }
  }

  /// Update an existing habit
  Future<void> updateHabit(String userId, Habit habit) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('habits')
          .doc(habit.id)
          .update(habit.toMap());
    } catch (e) {
      throw Exception('Failed to update habit: $e');
    }
  }

  /// Delete a habit
  Future<void> deleteHabit(String userId, String habitId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('habits')
          .doc(habitId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete habit: $e');
    }
  }
}
