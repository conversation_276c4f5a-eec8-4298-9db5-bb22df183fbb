import 'package:adaa/domain/entities/day.dart';

class DayModel extends Day {
  const DayModel({
    required super.id,
    required super.date,
    required super.completedHabits,
    required super.manualEvaluation,
    required super.habitSnapshot,
  });

  factory DayModel.fromJson(Map<String, dynamic> json) {
    return DayModel(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      completedHabits: List<String>.from(json['completedHabits'] ?? []),
      manualEvaluation: (json['manualEvaluation'] as num?)?.toDouble() ?? 0.0,
      habitSnapshot:
          (json['habitSnapshot'] as List<dynamic>?)
              ?.map((e) => HabitSnapshot.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String().split('T')[0], // Store as YYYY-MM-DD
      'completedHabits': completedHabits,
      'manualEvaluation': manualEvaluation,
      'habitSnapshot': habitSnapshot.map((e) => e.toJson()).toList(),
    };
  }

  @override
  DayModel copyWith({
    String? id,
    DateTime? date,
    List<String>? completedHabits,
    double? manualEvaluation,
    List<HabitSnapshot>? habitSnapshot,
    double? calculatedScore,
  }) {
    return DayModel(
      id: id ?? this.id,
      date: date ?? this.date,
      completedHabits: completedHabits ?? this.completedHabits,
      manualEvaluation: manualEvaluation ?? this.manualEvaluation,
      habitSnapshot: habitSnapshot ?? this.habitSnapshot,
    );
  }
}
