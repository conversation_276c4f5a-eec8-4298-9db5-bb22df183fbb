import 'package:adaa/data/datasources/firebase_daily_log_datasource.dart';
import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/data/models/daily_log_model.dart';
import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';

class DailyLogRepositoryImpl implements DailyLogRepository {
  final FirebaseDailyLogDataSource _dataSource;
  final FirebaseHabitDataSource _habitDataSource;

  DailyLogRepositoryImpl(this._dataSource, this._habitDataSource);

  // Calculate max possible score for a day
  Future<double> _calculateMaxPossibleScore(String userId) async {
    final activeHabits = await _habitDataSource.getHabits(userId).first;
    return activeHabits.fold(0.0, (sum, habit) => sum + habit.points) +
        3.0; // 3.0 is the max manual score
  }

  @override
  Future<Day?> getDailyLog(String userId, DateTime date) async {
    return await _dataSource.getDailyLog(userId, date);
  }

  @override
  Stream<List<Day>> getDailyLogsForRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async* {
    final result = await _dataSource.getDailyLogsForRange(
      userId,
      startDate,
      endDate,
    );
    yield result;
  }

  @override
  Future<void> saveDailyLog(String userId, Day day) async {
    await _dataSource.saveDailyLog(day as DayModel);
  }

  @override
  Future<void> markHabitAsCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    final habit = await _habitDataSource.getHabitById(userId, habitId);
    if (habit != null) {
      final maxPossibleScore = await _calculateMaxPossibleScore(userId);
      await _dataSource.markHabitAsCompleted(
        userId,
        habitId,
        date,
        habit.points,
        maxPossibleScore,
      );
    }
  }

  @override
  Future<void> markHabitAsNotCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    final habit = await _habitDataSource.getHabitById(userId, habitId);
    if (habit != null) {
      final maxPossibleScore = await _calculateMaxPossibleScore(userId);
      await _dataSource.markHabitAsNotCompleted(
        userId,
        habitId,
        date,
        habit.points,
        maxPossibleScore,
      );
    }
  }

  @override
  Future<void> updateManualScore(
    String userId,
    DateTime date,
    double manualScore,
  ) async {
    final maxPossibleScore = await _calculateMaxPossibleScore(userId);
    await _dataSource.updateManualScore(
      userId,
      date,
      manualScore,
      maxPossibleScore,
    );
  }
}
