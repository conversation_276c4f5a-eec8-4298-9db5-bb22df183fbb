import 'package:adaa/data/datasources/firebase_day_datasource.dart';
import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';

/// Firebase implementation of DailyLogRepository
class FirebaseDayRepository implements DailyLogRepository {
  final FirebaseDayDataSource _dayDataSource;
  final FirebaseHabitDataSource _habitDataSource;

  FirebaseDayRepository(this._dayDataSource, this._habitDataSource);

  @override
  Future<Day?> getDailyLog(String userId, DateTime date) {
    return _dayDataSource.getDayByDate(userId, date);
  }

  @override
  Stream<List<Day>> getDailyLogsForRange(String userId, DateTime startDate, DateTime endDate) {
    return _dayDataSource.getDaysInRange(userId, startDate, endDate);
  }

  @override
  Future<void> saveDailyLog(String userId, Day day) {
    return _dayDataSource.saveDay(userId, day);
  }

  @override
  Future<void> markHabitAsCompleted(String userId, String habitId, DateTime date) async {
    // First, ensure the day exists with current habit snapshot
    await _ensureDayExists(userId, date);
    
    // Then mark the habit as completed
    await _dayDataSource.markHabitAsCompleted(userId, habitId, date);
  }

  @override
  Future<void> markHabitAsNotCompleted(String userId, String habitId, DateTime date) async {
    // First, ensure the day exists with current habit snapshot
    await _ensureDayExists(userId, date);
    
    // Then mark the habit as not completed
    await _dayDataSource.markHabitAsNotCompleted(userId, habitId, date);
  }

  @override
  Future<void> updateManualScore(String userId, DateTime date, double score) {
    return _dayDataSource.updateManualEvaluation(userId, date, score);
  }

  /// Ensure a day exists with current habit snapshot
  Future<void> _ensureDayExists(String userId, DateTime date) async {
    final existingDay = await _dayDataSource.getDayByDate(userId, date);
    
    if (existingDay == null) {
      // Get current active habits to create snapshot
      final activeHabits = await _habitDataSource.getHabits(userId).first;
      
      // Create day with current habit snapshot
      await _dayDataSource.createDayWithSnapshot(userId, date, activeHabits);
    }
  }
}
