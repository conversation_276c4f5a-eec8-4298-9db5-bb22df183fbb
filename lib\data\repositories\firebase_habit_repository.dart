import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/repositories/habit_repository.dart';

/// Firebase implementation of HabitRepository
class FirebaseHabitRepository implements HabitRepository {
  final FirebaseHabitDataSource _dataSource;

  FirebaseHabitRepository(this._dataSource);

  @override
  Stream<List<Habit>> getHabits(String userId) {
    return _dataSource.getHabits(userId);
  }

  @override
  Stream<List<Habit>> getActiveHabits(String userId) {
    // For now, return all habits since we simplified the schema
    // In a real app, you might want to add an isActive field
    return _dataSource.getHabits(userId);
  }

  @override
  Future<Habit?> getHabitById(String userId, String habitId) {
    return _dataSource.getHabitById(userId, habitId);
  }

  @override
  Future<String> addHabit(String userId, Habit habit) {
    return _dataSource.addHabit(userId, habit);
  }

  @override
  Future<void> updateHabit(String userId, Habit habit) {
    return _dataSource.updateHabit(userId, habit);
  }

  @override
  Future<void> deleteHabit(String userId, String habitId) {
    return _dataSource.deleteHabit(userId, habitId);
  }
}
