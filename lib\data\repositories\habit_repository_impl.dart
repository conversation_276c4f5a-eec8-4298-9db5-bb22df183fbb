import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/data/models/habit_model.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/repositories/habit_repository.dart';

class HabitRepositoryImpl implements HabitRepository {
  final FirebaseHabitDataSource _dataSource;

  HabitRepositoryImpl(this._dataSource);

  @override
  Stream<List<Habit>> getHabits(String userId) {
    return _dataSource.getHabits(userId);
  }

  @override
  Stream<List<Habit>> getActiveHabits(String userId) {
    return _dataSource.getHabits(userId);
  }

  @override
  Future<Habit?> getHabitById(String userId, String habitId) {
    return _dataSource.getHabitById(userId, habitId);
  }

  @override
  Future<String> addHabit(String userId, Habit habit) {
    return _dataSource.addHabit(userId, habit as HabitModel);
  }

  @override
  Future<void> updateHabit(String userId, Habit habit) {
    return _dataSource.updateHabit(userId, habit as HabitModel);
  }

  @override
  Future<void> deleteHabit(String userId, String habitId) {
    return _dataSource.deleteHabit(userId, habitId);
  }
}
