import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/data/datasources/firebase_day_datasource.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/entities/day.dart';

/// Complete Firebase service demonstrating the habit tracking process
/// This shows how habits are saved, days are recorded, and points are calculated
class FirebaseHabitService {
  final FirebaseHabitDataSource _habitDataSource;
  final FirebaseDayDataSource _dayDataSource;

  FirebaseHabitService(this._habitDataSource, this._dayDataSource);

  /// STEP 1: Create and save a new habit
  /// Example: Save "Fajr Prayer" habit with 1.0 points
  Future<String> createHabit({
    required String userId,
    required String name,
    required double points,
  }) async {
    print('📝 Creating habit: $name with $points points');
    
    final habit = Habit(
      id: '', // Will be generated by Firestore
      name: name,
      points: points,
      createdAt: DateTime.now(),
    );

    final habitId = await _habitDataSource.addHabit(userId, habit);
    print('✅ Habit created with ID: $habitId');
    
    return habitId;
  }

  /// STEP 2: Record a new day with habit tracking
  /// This creates a day document with habit snapshots and completed habits
  Future<void> recordDay({
    required String userId,
    required DateTime date,
    required List<String> completedHabitIds,
    double manualEvaluation = 0.0,
  }) async {
    print('📅 Recording day: ${Day.formatDateId(date)}');
    
    // Get current active habits to create snapshot
    final activeHabits = await _habitDataSource.getHabits(userId).first;
    print('📋 Found ${activeHabits.length} active habits');

    // Create habit snapshots
    final habitSnapshots = activeHabits.map((habit) => HabitSnapshot(
      id: habit.id,
      name: habit.name,
      points: habit.points,
    )).toList();

    // Create the day with snapshots
    final day = Day(
      id: Day.formatDateId(date),
      date: date,
      completedHabits: completedHabitIds,
      manualEvaluation: manualEvaluation,
      habitSnapshot: habitSnapshots,
    );

    // Calculate and log the score
    final calculatedScore = day.calculateScore();
    print('🎯 Calculated score: ${(calculatedScore * 100).toStringAsFixed(1)}%');

    await _dayDataSource.saveDay(userId, day);
    print('✅ Day recorded successfully');
  }

  /// STEP 3: Mark a habit as completed for today
  /// This updates the day's completed habits list and recalculates the score
  Future<void> markHabitCompleted({
    required String userId,
    required String habitId,
    required DateTime date,
  }) async {
    print('✅ Marking habit $habitId as completed for ${Day.formatDateId(date)}');
    
    // Ensure day exists with current habit snapshot
    await _ensureDayExists(userId, date);
    
    // Mark habit as completed
    await _dayDataSource.markHabitAsCompleted(userId, habitId, date);
    
    // Get updated day and log new score
    final updatedDay = await _dayDataSource.getDayByDate(userId, date);
    if (updatedDay != null) {
      final newScore = updatedDay.calculateScore();
      print('🎯 Updated score: ${(newScore * 100).toStringAsFixed(1)}%');
    }
  }

  /// STEP 4: Update manual evaluation for a day
  /// This updates the manual score (0-3) and recalculates the total score
  Future<void> updateManualEvaluation({
    required String userId,
    required DateTime date,
    required double manualEvaluation,
  }) async {
    print('📝 Updating manual evaluation to $manualEvaluation for ${Day.formatDateId(date)}');
    
    await _dayDataSource.updateManualEvaluation(userId, date, manualEvaluation);
    
    // Get updated day and log new score
    final updatedDay = await _dayDataSource.getDayByDate(userId, date);
    if (updatedDay != null) {
      final newScore = updatedDay.calculateScore();
      print('🎯 Updated score: ${(newScore * 100).toStringAsFixed(1)}%');
    }
  }

  /// DEMONSTRATION: Complete workflow example
  Future<void> demonstrateWorkflow(String userId) async {
    print('\n🚀 DEMONSTRATING COMPLETE HABIT TRACKING WORKFLOW\n');

    // Step 1: Create some habits
    final fajrHabitId = await createHabit(
      userId: userId,
      name: 'Fajr Prayer',
      points: 1.0,
    );

    final routineHabitId = await createHabit(
      userId: userId,
      name: 'Morning Routine',
      points: 1.0,
    );

    final exerciseHabitId = await createHabit(
      userId: userId,
      name: 'Exercise',
      points: 2.0,
    );

    print('\n');

    // Step 2: Record today with some completed habits
    final today = DateTime.now();
    await recordDay(
      userId: userId,
      date: today,
      completedHabitIds: [fajrHabitId, routineHabitId], // Completed 2 out of 3 habits
      manualEvaluation: 2.5, // Good manual evaluation
    );

    print('\n');

    // Step 3: Later, complete the exercise habit
    await markHabitCompleted(
      userId: userId,
      habitId: exerciseHabitId,
      date: today,
    );

    print('\n');

    // Step 4: Update manual evaluation
    await updateManualEvaluation(
      userId: userId,
      date: today,
      manualEvaluation: 3.0, // Perfect manual evaluation
    );

    print('\n✨ WORKFLOW COMPLETE!\n');

    // Show final calculation breakdown
    await _showCalculationBreakdown(userId, today);
  }

  /// Show detailed calculation breakdown
  Future<void> _showCalculationBreakdown(String userId, DateTime date) async {
    final day = await _dayDataSource.getDayByDate(userId, date);
    if (day == null) return;

    print('📊 CALCULATION BREAKDOWN:');
    print('─' * 40);

    // Show habit snapshots
    print('Habit Snapshots:');
    double totalPossiblePoints = 0;
    for (final snapshot in day.habitSnapshot) {
      print('  • ${snapshot.name}: ${snapshot.points} points');
      totalPossiblePoints += snapshot.points;
    }

    // Show completed habits
    print('\nCompleted Habits:');
    double completedPoints = 0;
    for (final habitId in day.completedHabits) {
      final snapshot = day.habitSnapshot.firstWhere((s) => s.id == habitId);
      print('  ✅ ${snapshot.name}: ${snapshot.points} points');
      completedPoints += snapshot.points;
    }

    // Show calculation
    print('\nCalculation:');
    print('  Completed Points: $completedPoints');
    print('  Manual Evaluation: ${day.manualEvaluation}');
    print('  Total Score: ${completedPoints + day.manualEvaluation}');
    print('  Max Possible: ${totalPossiblePoints + 3.0}');
    print('  Final Score: ${(day.calculateScore() * 100).toStringAsFixed(1)}%');
    print('─' * 40);
  }

  /// Ensure a day exists with current habit snapshot
  Future<void> _ensureDayExists(String userId, DateTime date) async {
    final existingDay = await _dayDataSource.getDayByDate(userId, date);
    
    if (existingDay == null) {
      // Get current active habits to create snapshot
      final activeHabits = await _habitDataSource.getHabits(userId).first;
      
      // Create day with current habit snapshot
      await _dayDataSource.createDayWithSnapshot(userId, date, activeHabits);
    }
  }
}
