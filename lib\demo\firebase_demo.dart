import 'package:flutter/material.dart';
import 'package:adaa/core/firebase/firebase_config.dart';
import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/data/datasources/firebase_day_datasource.dart';
import 'package:adaa/data/services/firebase_habit_service.dart';

/// Demo screen to test Firebase integration
class FirebaseDemo extends StatefulWidget {
  const FirebaseDemo({super.key});

  @override
  State<FirebaseDemo> createState() => _FirebaseDemoState();
}

class _FirebaseDemoState extends State<FirebaseDemo> {
  final List<String> _logs = [];
  bool _isRunning = false;
  late FirebaseHabitService _habitService;
  late String _userId;

  @override
  void initState() {
    super.initState();
    _initializeFirebase();
  }

  Future<void> _initializeFirebase() async {
    try {
      await FirebaseConfig.initialize();
      _userId = FirebaseConfig.getCurrentUserId();
      
      _habitService = FirebaseHabitService(
        FirebaseHabitDataSource(),
        FirebaseDayDataSource(),
      );
      
      _addLog('✅ Firebase initialized successfully');
      _addLog('👤 User ID: $_userId');
    } catch (e) {
      _addLog('❌ Firebase initialization failed: $e');
    }
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toLocal().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _runDemo() async {
    if (_isRunning) return;
    
    setState(() {
      _isRunning = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Starting Firebase demo...');

      // Create habits
      _addLog('📝 Creating habits...');
      final fajrId = await _habitService.createHabit(
        userId: _userId,
        name: 'Fajr Prayer',
        points: 1.0,
      );
      _addLog('✅ Created Fajr Prayer: ${fajrId.substring(0, 8)}...');

      final routineId = await _habitService.createHabit(
        userId: _userId,
        name: 'Morning Routine',
        points: 1.0,
      );
      _addLog('✅ Created Morning Routine: ${routineId.substring(0, 8)}...');

      final exerciseId = await _habitService.createHabit(
        userId: _userId,
        name: 'Exercise',
        points: 2.0,
      );
      _addLog('✅ Created Exercise: ${exerciseId.substring(0, 8)}...');

      // Record today
      _addLog('📅 Recording today...');
      await _habitService.recordDay(
        userId: _userId,
        date: DateTime.now(),
        completedHabitIds: [fajrId, routineId],
        manualEvaluation: 2.0,
      );
      _addLog('✅ Day recorded with 2/3 habits completed');

      // Mark exercise as completed
      _addLog('💪 Completing exercise...');
      await _habitService.markHabitCompleted(
        userId: _userId,
        habitId: exerciseId,
        date: DateTime.now(),
      );
      _addLog('✅ Exercise marked as completed');

      // Update manual evaluation
      _addLog('⭐ Updating manual evaluation...');
      await _habitService.updateManualEvaluation(
        userId: _userId,
        date: DateTime.now(),
        manualEvaluation: 3.0,
      );
      _addLog('✅ Manual evaluation updated to 3.0');

      _addLog('🎉 Demo completed successfully!');
      _addLog('📊 Final score: 100% (all habits + perfect manual)');

    } catch (e) {
      _addLog('❌ Demo failed: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Firebase Habit Tracking Demo',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  'This demo creates habits, records a day, marks habits as completed, and calculates scores using Firebase Firestore.',
                  style: TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isRunning ? null : _runDemo,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: _isRunning
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              SizedBox(width: 8),
                              Text('Running Demo...'),
                            ],
                          )
                        : const Text('Run Firebase Demo'),
                  ),
                ),
              ],
            ),
          ),
          const Divider(),
          Expanded(
            child: _logs.isEmpty
                ? const Center(
                    child: Text(
                      'Press "Run Firebase Demo" to start',
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _logs.length,
                    itemBuilder: (context, index) {
                      final log = _logs[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          log,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                            color: log.contains('❌') 
                                ? Colors.red 
                                : log.contains('✅') 
                                    ? Colors.green 
                                    : Colors.black87,
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
