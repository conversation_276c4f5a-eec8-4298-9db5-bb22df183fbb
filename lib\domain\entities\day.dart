import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// Represents a habit snapshot for a specific day
class HabitSnapshot extends Equatable {
  final String id;
  final String name;
  final double points;

  const HabitSnapshot({
    required this.id,
    required this.name,
    required this.points,
  });

  @override
  List<Object?> get props => [id, name, points];

  Map<String, dynamic> toMap() {
    return {'id': id, 'name': name, 'points': points};
  }

  factory HabitSnapshot.fromMap(Map<String, dynamic> map) {
    return HabitSnapshot(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      points: (map['points'] ?? 0.0).toDouble(),
    );
  }

  // JSON methods for compatibility
  Map<String, dynamic> toJson() => toMap();
  factory HabitSnapshot.fromJson(Map<String, dynamic> json) =>
      HabitSnapshot.fromMap(json);
}

/// Represents a day with habit tracking data
class Day extends Equatable {
  final String id; // yyyy-MM-dd format
  final DateTime date;
  final List<String> completedHabits; // List of habitId
  final double manualEvaluation; // 0 to 3
  final List<HabitSnapshot> habitSnapshot;
  final double? calculatedScore; // Optional calculated score

  const Day({
    required this.id,
    required this.date,
    required this.completedHabits,
    required this.manualEvaluation,
    required this.habitSnapshot,
    this.calculatedScore,
  });

  @override
  List<Object?> get props => [
    id,
    date,
    completedHabits,
    manualEvaluation,
    habitSnapshot,
    calculatedScore,
  ];

  Day copyWith({
    String? id,
    DateTime? date,
    List<String>? completedHabits,
    double? manualEvaluation,
    List<HabitSnapshot>? habitSnapshot,
    double? calculatedScore,
  }) {
    return Day(
      id: id ?? this.id,
      date: date ?? this.date,
      completedHabits: completedHabits ?? this.completedHabits,
      manualEvaluation: manualEvaluation ?? this.manualEvaluation,
      habitSnapshot: habitSnapshot ?? this.habitSnapshot,
      calculatedScore: calculatedScore ?? this.calculatedScore,
    );
  }

  /// Calculate the score using the formula:
  /// (Total habit points completed from habitSnapshot + manualEvaluation) ÷ (Total of all snapshot points + 3)
  double calculateScore() {
    // Get total points from completed habits
    double completedPoints = 0.0;
    for (String habitId in completedHabits) {
      final snapshot = habitSnapshot.firstWhere(
        (s) => s.id == habitId,
        orElse: () => const HabitSnapshot(id: '', name: '', points: 0.0),
      );
      completedPoints += snapshot.points;
    }

    // Get total possible points from all habits in snapshot
    double totalPossiblePoints = habitSnapshot.fold(
      0.0,
      (total, snapshot) => total + snapshot.points,
    );

    // Calculate score: (completed points + manual evaluation) / (total possible + 3)
    double totalScore = completedPoints + manualEvaluation;
    double maxPossibleScore =
        totalPossiblePoints + 3.0; // 3 is max manual evaluation

    return maxPossibleScore > 0 ? totalScore / maxPossibleScore : 0.0;
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'date': Timestamp.fromDate(date),
      'completedHabits': completedHabits,
      'manualEvaluation': manualEvaluation,
      'habitSnapshot': habitSnapshot.map((h) => h.toMap()).toList(),
      'calculatedScore': calculatedScore ?? calculateScore(),
    };
  }

  // Create from Map (Firestore document)
  factory Day.fromMap(Map<String, dynamic> map, String id) {
    return Day(
      id: id,
      date: (map['date'] as Timestamp).toDate(),
      completedHabits: List<String>.from(map['completedHabits'] ?? []),
      manualEvaluation: (map['manualEvaluation'] ?? 0.0).toDouble(),
      habitSnapshot:
          (map['habitSnapshot'] as List<dynamic>?)
              ?.map(
                (item) => HabitSnapshot.fromMap(item as Map<String, dynamic>),
              )
              .toList() ??
          [],
      calculatedScore: (map['calculatedScore'] as num?)?.toDouble(),
    );
  }

  /// Format date as yyyy-MM-dd for document ID
  static String formatDateId(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-'
        '${date.month.toString().padLeft(2, '0')}-'
        '${date.day.toString().padLeft(2, '0')}';
  }

  /// Parse date from yyyy-MM-dd format
  static DateTime parseDateId(String dateId) {
    final parts = dateId.split('-');
    return DateTime(
      int.parse(parts[0]),
      int.parse(parts[1]),
      int.parse(parts[2]),
    );
  }
}
