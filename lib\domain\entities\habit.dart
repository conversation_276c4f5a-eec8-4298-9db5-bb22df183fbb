import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

class Habit extends Equatable {
  final String id;
  final String name;
  final double points;
  final DateTime createdAt;

  const Habit({
    required this.id,
    required this.name,
    required this.points,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [id, name, points, createdAt];

  Habit copyWith({
    String? id,
    String? name,
    double? points,
    DateTime? createdAt,
  }) {
    return Habit(
      id: id ?? this.id,
      name: name ?? this.name,
      points: points ?? this.points,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'points': points,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  // Create from Map (Firestore document)
  factory Habit.fromMap(Map<String, dynamic> map, String id) {
    return Habit(
      id: id,
      name: map['name'] ?? '',
      points: (map['points'] ?? 0.0).toDouble(),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }
}
