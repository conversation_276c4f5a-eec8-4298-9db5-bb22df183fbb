import 'package:adaa/domain/entities/day.dart';

abstract class DailyLogRepository {
  // Get daily log for a specific date
  Future<Day?> getDailyLog(String userId, DateTime date);

  // Get daily logs for a date range
  Stream<List<Day>> getDailyLogsForRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );

  // Save or update a daily log
  Future<void> saveDailyLog(String userId, Day day);

  // Mark a habit as completed for a specific date
  Future<void> markHabitAsCompleted(
    String userId,
    String habitId,
    DateTime date,
  );

  // Mark a habit as not completed for a specific date
  Future<void> markHabitAsNotCompleted(
    String userId,
    String habitId,
    DateTime date,
  );

  // Update manual score for a specific date
  Future<void> updateManualScore(
    String userId,
    DateTime date,
    double manualScore,
  );
}
