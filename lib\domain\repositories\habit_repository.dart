import 'package:adaa/domain/entities/habit.dart';

abstract class HabitRepository {
  // Get all habits for a user
  Stream<List<Habit>> getHabits(String userId);

  // Get active habits for a user
  Stream<List<Habit>> getActiveHabits(String userId);

  // Get a single habit by ID
  Future<Habit?> getHabitById(String userId, String habitId);

  // Add a new habit
  Future<String> addHabit(String userId, Habit habit);

  // Update an existing habit
  Future<void> updateHabit(String userId, Habit habit);

  // Delete a habit
  Future<void> deleteHabit(String userId, String habitId);
}
