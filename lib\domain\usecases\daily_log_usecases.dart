import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';

class GetDailyLogUseCase {
  final DailyLogRepository repository;

  GetDailyLogUseCase(this.repository);

  Future<Day?> call(String userId, DateTime date) {
    return repository.getDailyLog(userId, date);
  }
}

class GetDailyLogsForRangeUseCase {
  final DailyLogRepository repository;

  GetDailyLogsForRangeUseCase(this.repository);

  Stream<List<Day>> call(String userId, DateTime startDate, DateTime endDate) {
    return repository.getDailyLogsForRange(userId, startDate, endDate);
  }
}

class SaveDailyLogUseCase {
  final DailyLogRepository repository;

  SaveDailyLogUseCase(this.repository);

  Future<void> call(String userId, Day day) {
    return repository.saveDailyLog(userId, day);
  }
}

class MarkHabitAsCompletedUseCase {
  final DailyLogRepository repository;

  MarkHabitAsCompletedUseCase(this.repository);

  Future<void> call(String userId, String habitId, DateTime date) {
    return repository.markHabitAsCompleted(userId, habitId, date);
  }
}

class MarkHabitAsNotCompletedUseCase {
  final DailyLogRepository repository;

  MarkHabitAsNotCompletedUseCase(this.repository);

  Future<void> call(String userId, String habitId, DateTime date) {
    return repository.markHabitAsNotCompleted(userId, habitId, date);
  }
}

class UpdateManualScoreUseCase {
  final DailyLogRepository repository;

  UpdateManualScoreUseCase(this.repository);

  Future<void> call(String userId, DateTime date, double manualScore) {
    return repository.updateManualScore(userId, date, manualScore);
  }
}
