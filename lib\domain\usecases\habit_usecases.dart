import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/repositories/habit_repository.dart';

class GetHabitsUseCase {
  final HabitRepository repository;

  GetHabitsUseCase(this.repository);

  Stream<List<Habit>> call(String userId) {
    return repository.getHabits(userId);
  }
}

class GetActiveHabitsUseCase {
  final HabitRepository repository;

  GetActiveHabitsUseCase(this.repository);

  Stream<List<Habit>> call(String userId) {
    return repository.getActiveHabits(userId);
  }
}

class GetHabitByIdUseCase {
  final HabitRepository repository;

  GetHabitByIdUseCase(this.repository);

  Future<Habit?> call(String userId, String habitId) {
    return repository.getHabitById(userId, habitId);
  }
}

class AddHabitUseCase {
  final HabitRepository repository;

  AddHabitUseCase(this.repository);

  Future<String> call(String userId, Habit habit) {
    return repository.addHabit(userId, habit);
  }
}

class UpdateHabitUseCase {
  final HabitRepository repository;

  UpdateHabitUseCase(this.repository);

  Future<void> call(String userId, Habit habit) {
    return repository.updateHabit(userId, habit);
  }
}

class DeleteHabitUseCase {
  final HabitRepository repository;

  DeleteHabitUseCase(this.repository);

  Future<void> call(String userId, String habitId) {
    return repository.deleteHabit(userId, habitId);
  }
}
