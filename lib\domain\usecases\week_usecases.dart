// Week functionality temporarily disabled
// TODO: Implement week functionality with proper entities

/*
import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/week.dart';
import 'package:adaa/domain/repositories/daily_log_repository.dart';

class GetWeeksUseCase {
  final WeekRepository repository;

  GetWeeksUseCase(this.repository);

  Future<List<WeekSummary>> call(String userId) {
    return repository.getWeeks(userId);
  }
}

class GetWeekByIdUseCase {
  final WeekRepository repository;

  GetWeekByIdUseCase(this.repository);

  Future<WeekSummary?> call(String weekId) {
    return repository.getWeekById(weekId);
  }
}

class GetWeekForDateUseCase {
  final WeekRepository repository;

  GetWeekForDateUseCase(this.repository);

  Future<WeekSummary?> call(String userId, DateTime date) {
    return repository.getWeekForDate(userId, date);
  }
}

class SaveWeekSummaryUseCase {
  final WeekRepository repository;

  SaveWeekSummaryUseCase(this.repository);

  Future<void> call(WeekSummary weekSummary) {
    return repository.saveWeekSummary(weekSummary);
  }
}
*/

/*
class CalculateWeekSummaryUseCase {
  final DailyLogRepository dailyLogRepository;

  CalculateWeekSummaryUseCase(this.dailyLogRepository);

  Future<WeekSummary> call(String userId, DateTime weekStartDate) async {
    // Calculate the week's end date (7 days from start)
    final weekEndDate = weekStartDate.add(const Duration(days: 6));

    // Get all daily logs for the week
    final dailyLogs = await dailyLogRepository.getDailyLogsForRange(
      userId,
      weekStartDate,
      weekEndDate,
    );

    // Create a map of date to daily log
    final Map<DateTime, DailyLog> dateToLogMap = {};
    for (var log in dailyLogs) {
      dateToLogMap[_normalizeDate(log.date)] = log;
    }

    // Create day scores for each day of the week
    final List<DayScore> dayScores = [];
    double totalPercentage = 0;
    int daysWithLogs = 0;

    for (int i = 0; i < 7; i++) {
      final date = weekStartDate.add(Duration(days: i));
      final normalizedDate = _normalizeDate(date);

      if (dateToLogMap.containsKey(normalizedDate)) {
        final log = dateToLogMap[normalizedDate]!;
        final percentage = log.percentage;

        dayScores.add(
          DayScore(
            date: normalizedDate,
            score: log.totalScore,
            maxPossibleScore: log.maxPossibleScore,
            percentage: percentage,
          ),
        );

        totalPercentage += percentage;
        daysWithLogs++;
      } else {
        // No log for this day, add a zero score
        dayScores.add(
          DayScore(
            date: normalizedDate,
            score: 0,
            maxPossibleScore: 0,
            percentage: 0,
          ),
        );
      }
    }

    // Calculate average percentage
    final double averagePercentage =
        daysWithLogs > 0 ? totalPercentage / daysWithLogs : 0.0;

    // Determine grade based on average percentage
    final grade = _calculateGrade(averagePercentage);

    // Create week ID in format YYYY-Wxx (e.g., 2025-W19)
    final weekYear = weekStartDate.year;
    final weekNumber = _getWeekNumber(weekStartDate);
    final weekId = '$weekYear-W$weekNumber';

    return WeekSummary(
      id: weekId,
      userId: userId,
      startDate: weekStartDate,
      endDate: weekEndDate,
      days: dayScores,
      averagePercentage: averagePercentage,
      grade: grade,
    );
  }

  // Helper method to normalize date by removing time component
  DateTime _normalizeDate(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  // Helper method to calculate grade based on percentage
  String _calculateGrade(double percentage) {
    if (percentage >= 90) {
      return 'A';
    } else if (percentage >= 80) {
      return 'B';
    } else if (percentage >= 70) {
      return 'C';
    } else if (percentage >= 60) {
      return 'D';
    } else {
      return 'F';
    }
  }

  // Helper method to get ISO week number
  int _getWeekNumber(DateTime date) {
    final dayOfYear =
        int.parse(
          date.difference(DateTime(date.year, 1, 1)).inDays.toString(),
        ) +
        1;
    return ((dayOfYear - date.weekday + 10) / 7).floor();
  }
}
*/
