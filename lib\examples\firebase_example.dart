import 'package:firebase_core/firebase_core.dart';
import 'package:adaa/data/datasources/firebase_habit_datasource.dart';
import 'package:adaa/data/datasources/firebase_day_datasource.dart';
import 'package:adaa/data/services/firebase_habit_service.dart';

/// Example demonstrating how to use the Firebase habit tracking system
/// This shows the complete process from creating habits to calculating scores
class FirebaseExample {
  
  /// Initialize Firebase and run the example
  static Future<void> runExample() async {
    print('🔥 Initializing Firebase...');
    
    // Initialize Firebase (you need to configure this for your project)
    await Firebase.initializeApp();
    
    // Create data sources
    final habitDataSource = FirebaseHabitDataSource();
    final dayDataSource = FirebaseDayDataSource();
    
    // Create the service
    final habitService = FirebaseHabitService(habitDataSource, dayDataSource);
    
    // Run the demonstration
    const userId = 'demo-user-123';
    await habitService.demonstrateWorkflow(userId);
  }

  /// Example of manual habit and day management
  static Future<void> manualExample() async {
    await Firebase.initializeApp();
    
    final habitDataSource = FirebaseHabitDataSource();
    final dayDataSource = FirebaseDayDataSource();
    const userId = 'manual-user-456';

    print('\n📚 MANUAL EXAMPLE: Step-by-step habit tracking\n');

    // 1. Create habits
    print('1️⃣ Creating habits...');
    final fajrId = await habitDataSource.addHabit(userId, Habit(
      id: '',
      name: 'Fajr Prayer',
      points: 1.0,
      createdAt: DateTime.now(),
    ));
    
    final routineId = await habitDataSource.addHabit(userId, Habit(
      id: '',
      name: 'Morning Routine',
      points: 1.0,
      createdAt: DateTime.now(),
    ));

    print('   ✅ Created Fajr Prayer habit: $fajrId');
    print('   ✅ Created Morning Routine habit: $routineId');

    // 2. Create a day with habit snapshot
    print('\n2️⃣ Creating day with habit snapshot...');
    final today = DateTime.now();
    final activeHabits = await habitDataSource.getHabits(userId).first;
    
    await dayDataSource.createDayWithSnapshot(
      userId,
      today,
      activeHabits,
      completedHabits: [fajrId], // Only completed Fajr
      manualEvaluation: 1.5,
    );
    print('   ✅ Day created with habit snapshot');

    // 3. Mark another habit as completed
    print('\n3️⃣ Marking routine as completed...');
    await dayDataSource.markHabitAsCompleted(userId, routineId, today);
    print('   ✅ Morning Routine marked as completed');

    // 4. Update manual evaluation
    print('\n4️⃣ Updating manual evaluation...');
    await dayDataSource.updateManualEvaluation(userId, today, 2.5);
    print('   ✅ Manual evaluation updated to 2.5');

    // 5. Show final result
    print('\n5️⃣ Final result:');
    final finalDay = await dayDataSource.getDayByDate(userId, today);
    if (finalDay != null) {
      final score = finalDay.calculateScore();
      print('   📊 Final score: ${(score * 100).toStringAsFixed(1)}%');
      print('   📋 Completed habits: ${finalDay.completedHabits.length}/${finalDay.habitSnapshot.length}');
      print('   ⭐ Manual evaluation: ${finalDay.manualEvaluation}/3.0');
    }

    print('\n✨ Manual example complete!\n');
  }

  /// Example showing score calculation details
  static void scoreCalculationExample() {
    print('\n🧮 SCORE CALCULATION EXAMPLE\n');

    // Create example habit snapshots
    final habitSnapshots = [
      HabitSnapshot(id: '1', name: 'Fajr Prayer', points: 1.0),
      HabitSnapshot(id: '2', name: 'Morning Routine', points: 1.0),
      HabitSnapshot(id: '3', name: 'Exercise', points: 2.0),
    ];

    // Example 1: All habits completed, perfect manual evaluation
    print('Example 1: Perfect day');
    final perfectDay = Day(
      id: '2024-01-15',
      date: DateTime(2024, 1, 15),
      completedHabits: ['1', '2', '3'], // All completed
      manualEvaluation: 3.0, // Perfect manual score
      habitSnapshot: habitSnapshots,
    );
    
    print('  Completed: ${perfectDay.completedHabits.length}/3 habits');
    print('  Manual: ${perfectDay.manualEvaluation}/3.0');
    print('  Score: ${(perfectDay.calculateScore() * 100).toStringAsFixed(1)}%');
    print('  Calculation: (1.0 + 1.0 + 2.0 + 3.0) ÷ (1.0 + 1.0 + 2.0 + 3.0) = 7.0 ÷ 7.0 = 100%\n');

    // Example 2: Partial completion
    print('Example 2: Partial completion');
    final partialDay = Day(
      id: '2024-01-16',
      date: DateTime(2024, 1, 16),
      completedHabits: ['1', '2'], // 2 out of 3 completed
      manualEvaluation: 2.0, // Good manual score
      habitSnapshot: habitSnapshots,
    );
    
    print('  Completed: ${partialDay.completedHabits.length}/3 habits');
    print('  Manual: ${partialDay.manualEvaluation}/3.0');
    print('  Score: ${(partialDay.calculateScore() * 100).toStringAsFixed(1)}%');
    print('  Calculation: (1.0 + 1.0 + 2.0) ÷ (1.0 + 1.0 + 2.0 + 3.0) = 4.0 ÷ 7.0 = 57.1%\n');

    // Example 3: Poor day
    print('Example 3: Challenging day');
    final poorDay = Day(
      id: '2024-01-17',
      date: DateTime(2024, 1, 17),
      completedHabits: ['1'], // Only 1 completed
      manualEvaluation: 0.5, // Low manual score
      habitSnapshot: habitSnapshots,
    );
    
    print('  Completed: ${poorDay.completedHabits.length}/3 habits');
    print('  Manual: ${poorDay.manualEvaluation}/3.0');
    print('  Score: ${(poorDay.calculateScore() * 100).toStringAsFixed(1)}%');
    print('  Calculation: (1.0 + 0.5) ÷ (1.0 + 1.0 + 2.0 + 3.0) = 1.5 ÷ 7.0 = 21.4%\n');

    print('✨ Score calculation examples complete!\n');
  }
}

/// Import this for the Day and HabitSnapshot classes
import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/habit.dart';
