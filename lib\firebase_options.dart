// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCGwdsVXllr6Yg-9KQBCX9fGk-YstK2H5Q',
    appId: '1:981674950072:android:d3f1402992ef162eb7234d',
    messagingSenderId: '981674950072',
    projectId: 'adaa-605a6',
    storageBucket: 'adaa-605a6.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBeKJ0RTcF_6mctW5O56BQt5SpzxOP1fcI',
    appId: '1:981674950072:ios:4df3ac00b8960493b7234d',
    messagingSenderId: '981674950072',
    projectId: 'adaa-605a6',
    storageBucket: 'adaa-605a6.firebasestorage.app',
    iosBundleId: 'com.example.adaa',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCGwdsVXllr6Yg-9KQBCX9fGk-YstK2H5Q',
    appId: '1:981674950072:web:demo',
    messagingSenderId: '981674950072',
    projectId: 'adaa-605a6',
    authDomain: 'adaa-605a6.firebaseapp.com',
    storageBucket: 'adaa-605a6.firebasestorage.app',
  );
}
