import 'package:adaa/core/constants/app_theme.dart';
import 'package:adaa/core/di/injection_container.dart' as di;
import 'package:adaa/presentation/blocs/auth/auth_cubit.dart';
import 'package:adaa/presentation/blocs/daily_log/daily_log_cubit.dart';
import 'package:adaa/presentation/blocs/habits/habits_cubit.dart';
import 'package:adaa/presentation/screens/home_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize dependency injection
  await di.init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(
          create: (context) => AuthCubit(firebaseAuth: di.sl()),
        ),
        BlocProvider<HabitsCubit>(
          create:
              (context) => HabitsCubit(
                getHabitsUseCase: di.sl(),
                getActiveHabitsUseCase: di.sl(),
                addHabitUseCase: di.sl(),
                updateHabitUseCase: di.sl(),
                deleteHabitUseCase: di.sl(),
              ),
        ),
        BlocProvider<DailyLogCubit>(
          create:
              (context) => DailyLogCubit(
                getDailyLogUseCase: di.sl(),
                getActiveHabitsUseCase: di.sl(),
                markHabitAsCompletedUseCase: di.sl(),
                markHabitAsNotCompletedUseCase: di.sl(),
                updateManualScoreUseCase: di.sl(),
              ),
        ),
      ],
      child: MaterialApp(
        title: 'Adaa - Habit Tracker',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const HomeScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
