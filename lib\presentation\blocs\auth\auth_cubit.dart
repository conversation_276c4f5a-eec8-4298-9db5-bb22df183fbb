import 'dart:async';

import 'package:adaa/presentation/blocs/auth/auth_state.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AuthCubit extends Cubit<AuthState> {
  final FirebaseAuth _firebaseAuth;
  StreamSubscription<User?>? _authStateSubscription;

  AuthCubit({required FirebaseAuth firebaseAuth})
    : _firebaseAuth = firebaseAuth,
      super(const AuthInitial()) {
    _initializeAuth();
  }

  void _initializeAuth() {
    _authStateSubscription = _firebaseAuth.authStateChanges().listen((user) {
      if (user != null) {
        emit(Authenticated(user));
      } else {
        emit(const Unauthenticated());
      }
    });
  }

  Future<void> signInAnonymously() async {
    try {
      emit(const AuthLoading());
      await _firebaseAuth.signInAnonymously();
      // State will be updated automatically via authStateChanges
    } catch (e) {
      debugPrint('Sign in error: $e');
      emit(AuthError(e.toString()));
    }
  }

  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      // State will be updated automatically via authStateChanges
    } catch (e) {
      debugPrint('Sign out error: $e');
      emit(AuthError(e.toString()));
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
