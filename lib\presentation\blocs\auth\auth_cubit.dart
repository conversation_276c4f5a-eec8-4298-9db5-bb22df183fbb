import 'dart:async';

import 'package:adaa/presentation/blocs/auth/auth_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Mock User class for when Firebase Auth is not available
class MockUser {
  final String uid;
  final String email;

  MockUser({required this.uid, this.email = '<EMAIL>'});
}

// Mock FirebaseAuth class for when Firebase is not available
class MockFirebaseAuth {
  // Mock implementation - does nothing
}

class AuthCubit extends Cubit<AuthState> {
  AuthCubit({MockFirebaseAuth? firebaseAuth}) : super(const AuthInitial()) {
    // Use mock authentication
    emit(const Unauthenticated());
  }

  Future<void> signInAnonymously() async {
    try {
      emit(const AuthLoading());

      // Mock sign in
      await Future.delayed(const Duration(milliseconds: 500));
      final mockUser = MockUser(
        uid: 'mock-user-${DateTime.now().millisecondsSinceEpoch}',
      );
      emit(Authenticated(mockUser));
    } catch (e) {
      debugPrint('Sign in error: $e');
      emit(AuthError(e.toString()));
    }
  }

  Future<void> signOut() async {
    try {
      // Mock sign out
      await Future.delayed(const Duration(milliseconds: 300));
      emit(const Unauthenticated());
    } catch (e) {
      debugPrint('Sign out error: $e');
      emit(AuthError(e.toString()));
    }
  }
}
