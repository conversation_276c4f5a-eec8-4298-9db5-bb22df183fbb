import 'dart:async';

import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/usecases/daily_log_usecases.dart';
import 'package:adaa/domain/usecases/habit_usecases.dart';
import 'package:adaa/presentation/blocs/daily_log/daily_log_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DailyLogCubit extends Cubit<DailyLogState> {
  final GetDailyLogUseCase _getDailyLogUseCase;
  final GetActiveHabitsUseCase _getActiveHabitsUseCase;
  final MarkHabitAsCompletedUseCase _markHabitAsCompletedUseCase;
  final MarkHabitAsNotCompletedUseCase _markHabitAsNotCompletedUseCase;
  final UpdateManualScoreUseCase _updateManualScoreUseCase;

  StreamSubscription? _habitsSubscription;

  DailyLogCubit({
    required GetDailyLogUseCase getDailyLogUseCase,
    required GetActiveHabitsUseCase getActiveHabitsUseCase,
    required MarkHabitAsCompletedUseCase markHabitAsCompletedUseCase,
    required MarkHabitAsNotCompletedUseCase markHabitAsNotCompletedUseCase,
    required UpdateManualScoreUseCase updateManualScoreUseCase,
  }) : _getDailyLogUseCase = getDailyLogUseCase,
       _getActiveHabitsUseCase = getActiveHabitsUseCase,
       _markHabitAsCompletedUseCase = markHabitAsCompletedUseCase,
       _markHabitAsNotCompletedUseCase = markHabitAsNotCompletedUseCase,
       _updateManualScoreUseCase = updateManualScoreUseCase,
       super(const DailyLogInitial());

  Future<void> loadDailyLog(String userId, DateTime date) async {
    emit(const DailyLogLoading());

    try {
      // Load active habits
      List<Habit> activeHabits = [];
      _habitsSubscription?.cancel();

      _habitsSubscription = _getActiveHabitsUseCase(userId).listen(
        (habits) async {
          activeHabits = habits;

          // Load daily log
          final dailyLog = await _getDailyLogUseCase(userId, date);

          emit(
            DailyLogLoaded(
              dailyLog: dailyLog ?? _createEmptyDay(userId, date, activeHabits),
              activeHabits: activeHabits,
              selectedDate: date,
            ),
          );
        },
        onError: (error) {
          emit(DailyLogError(error.toString()));
        },
      );
    } catch (e) {
      emit(DailyLogError(e.toString()));
    }
  }

  Future<void> markHabitAsCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    emit(const DailyLogActionInProgress());
    try {
      await _markHabitAsCompletedUseCase(userId, habitId, date);
      await loadDailyLog(userId, date);
    } catch (e) {
      emit(DailyLogActionFailure(e.toString()));
    }
  }

  Future<void> markHabitAsNotCompleted(
    String userId,
    String habitId,
    DateTime date,
  ) async {
    emit(const DailyLogActionInProgress());
    try {
      await _markHabitAsNotCompletedUseCase(userId, habitId, date);
      await loadDailyLog(userId, date);
    } catch (e) {
      emit(DailyLogActionFailure(e.toString()));
    }
  }

  Future<void> updateManualScore(
    String userId,
    DateTime date,
    double manualScore,
  ) async {
    emit(const DailyLogActionInProgress());
    try {
      await _updateManualScoreUseCase(userId, date, manualScore);
      await loadDailyLog(userId, date);
    } catch (e) {
      emit(DailyLogActionFailure(e.toString()));
    }
  }

  // Helper method to create an empty day
  Day _createEmptyDay(String userId, DateTime date, List<Habit> activeHabits) {
    // Create habit snapshots from active habits
    final habitSnapshots =
        activeHabits
            .map(
              (habit) => HabitSnapshot(
                id: habit.id,
                name: habit.name,
                points: habit.points,
              ),
            )
            .toList();

    return Day(
      id: Day.formatDateId(date),
      date: date,
      completedHabits: [],
      manualEvaluation: 0.0,
      habitSnapshot: habitSnapshots,
    );
  }

  @override
  Future<void> close() {
    _habitsSubscription?.cancel();
    return super.close();
  }
}
