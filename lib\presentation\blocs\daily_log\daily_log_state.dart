import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:equatable/equatable.dart';

abstract class DailyLogState extends Equatable {
  const DailyLogState();

  @override
  List<Object?> get props => [];
}

class DailyLogInitial extends DailyLogState {
  const DailyLogInitial();
}

class DailyLogLoading extends DailyLogState {
  const DailyLogLoading();
}

class DailyLogLoaded extends DailyLogState {
  final Day dailyLog;
  final List<Habit> activeHabits;
  final DateTime selectedDate;

  const DailyLogLoaded({
    required this.dailyLog,
    required this.activeHabits,
    required this.selectedDate,
  });

  @override
  List<Object?> get props => [dailyLog, activeHabits, selectedDate];
}

class DailyLogError extends DailyLogState {
  final String message;

  const DailyLogError(this.message);

  @override
  List<Object?> get props => [message];
}

class DailyLogActionInProgress extends DailyLogState {
  const DailyLogActionInProgress();
}

class DailyLogActionSuccess extends DailyLogState {
  final String message;

  const DailyLogActionSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class DailyLogActionFailure extends DailyLogState {
  final String message;

  const DailyLogActionFailure(this.message);

  @override
  List<Object?> get props => [message];
}
