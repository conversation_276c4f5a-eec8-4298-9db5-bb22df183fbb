import 'dart:async';

import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/domain/usecases/habit_usecases.dart';
import 'package:adaa/presentation/blocs/habits/habits_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HabitsCubit extends Cubit<HabitsState> {
  final GetHabitsUseCase _getHabitsUseCase;
  final GetActiveHabitsUseCase _getActiveHabitsUseCase;
  final AddHabitUseCase _addHabitUseCase;
  final UpdateHabitUseCase _updateHabitUseCase;
  final DeleteHabitUseCase _deleteHabitUseCase;

  StreamSubscription? _habitsSubscription;

  HabitsCubit({
    required GetHabitsUseCase getHabitsUseCase,
    required GetActiveHabitsUseCase getActiveHabitsUseCase,
    required AddHabitUseCase addHabitUseCase,
    required UpdateHabitUseCase updateHabitUseCase,
    required DeleteHabitUseCase deleteHabitUseCase,
  }) : _getHabitsUseCase = getHabitsUseCase,
       _getActiveHabitsUseCase = getActiveHabitsUseCase,
       _addHabitUseCase = addHabitUseCase,
       _updateHabitUseCase = updateHabitUseCase,
       _deleteHabitUseCase = deleteHabitUseCase,
       super(const HabitsInitial());

  void loadHabits(String userId) {
    emit(const HabitsLoading());
    _habitsSubscription?.cancel();
    _habitsSubscription = _getHabitsUseCase(userId).listen(
      (habits) {
        emit(HabitsLoaded(habits));
      },
      onError: (error) {
        emit(HabitsError(error.toString()));
      },
    );
  }

  void loadActiveHabits(String userId) {
    emit(const HabitsLoading());
    _habitsSubscription?.cancel();
    _habitsSubscription = _getActiveHabitsUseCase(userId).listen(
      (habits) {
        emit(HabitsLoaded(habits));
      },
      onError: (error) {
        emit(HabitsError(error.toString()));
      },
    );
  }

  Future<void> addHabit({
    required String userId,
    required String name,
    required double points,
  }) async {
    emit(const HabitActionInProgress());
    try {
      final habit = Habit(
        id: '', // Will be generated by the repository
        name: name,
        points: points,
        createdAt: DateTime.now(),
      );
      await _addHabitUseCase(userId, habit);
      emit(const HabitActionSuccess('Habit added successfully'));
    } catch (e) {
      emit(HabitActionFailure(e.toString()));
    }
  }

  Future<void> updateHabit(String userId, Habit habit) async {
    emit(const HabitActionInProgress());
    try {
      await _updateHabitUseCase(userId, habit);
      emit(const HabitActionSuccess('Habit updated successfully'));
    } catch (e) {
      emit(HabitActionFailure(e.toString()));
    }
  }

  Future<void> deleteHabit(String userId, String habitId) async {
    emit(const HabitActionInProgress());
    try {
      await _deleteHabitUseCase(userId, habitId);
      emit(const HabitActionSuccess('Habit deleted successfully'));
    } catch (e) {
      emit(HabitActionFailure(e.toString()));
    }
  }

  @override
  Future<void> close() {
    _habitsSubscription?.cancel();
    return super.close();
  }
}
