import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/presentation/blocs/habits/habits_cubit.dart';
import 'package:adaa/presentation/blocs/habits/habits_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddEditHabitScreen extends StatefulWidget {
  final String userId;
  final Habit? habit;

  const AddEditHabitScreen({super.key, required this.userId, this.habit});

  @override
  State<AddEditHabitScreen> createState() => _AddEditHabitScreenState();
}

class _AddEditHabitScreenState extends State<AddEditHabitScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  double _dailyScore = 1.0;
  bool _isActive = true;

  bool get _isEditing => widget.habit != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _titleController.text = widget.habit!.name;
      _dailyScore = widget.habit!.points;
      // Remove description and isActive since they're not in the new Habit entity
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(_isEditing ? 'Edit Habit' : 'Add Habit')),
      body: BlocListener<HabitsCubit, HabitsState>(
        listener: (context, state) {
          if (state is HabitActionSuccess) {
            Navigator.of(context).pop();
          } else if (state is HabitActionFailure) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a title';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (optional)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Daily Score',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Slider(
                  value: _dailyScore,
                  min: 0.5,
                  max: 3.0,
                  divisions: 5,
                  label: _dailyScore.toString(),
                  onChanged: (value) {
                    setState(() {
                      _dailyScore = value;
                    });
                  },
                ),
                Text('Score: $_dailyScore', textAlign: TextAlign.center),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('Active'),
                  value: _isActive,
                  onChanged: (value) {
                    setState(() {
                      _isActive = value;
                    });
                  },
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _saveHabit,
                  child: Text(_isEditing ? 'Update' : 'Add'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _saveHabit() {
    if (_formKey.currentState!.validate()) {
      if (_isEditing) {
        final updatedHabit = widget.habit!.copyWith(
          name: _titleController.text,
          points: _dailyScore,
        );
        context.read<HabitsCubit>().updateHabit(widget.userId, updatedHabit);
      } else {
        context.read<HabitsCubit>().addHabit(
          userId: widget.userId,
          name: _titleController.text,
          points: _dailyScore,
        );
      }
    }
  }
}
