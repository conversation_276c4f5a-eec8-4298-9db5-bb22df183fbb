import 'package:adaa/core/constants/app_theme.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/presentation/blocs/auth/auth_cubit.dart';
import 'package:adaa/presentation/blocs/auth/auth_state.dart';
import 'package:adaa/presentation/blocs/habits/habits_cubit.dart';
import 'package:adaa/presentation/blocs/habits/habits_state.dart';
import 'package:adaa/presentation/screens/habits/add_edit_habit_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  @override
  void initState() {
    super.initState();
    _loadHabits();
  }

  void _loadHabits() {
    final authState = context.read<AuthCubit>().state;
    if (authState is Authenticated) {
      context.read<HabitsCubit>().loadHabits(authState.user.uid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('My Habits')),
      body: BlocConsumer<HabitsCubit, HabitsState>(
        listener: (context, state) {
          if (state is HabitActionSuccess) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
            _loadHabits();
          } else if (state is HabitActionFailure) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }
        },
        builder: (context, state) {
          if (state is HabitsLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is HabitsLoaded) {
            return _buildHabitsList(context, state.habits);
          } else if (state is HabitsError) {
            return Center(child: Text('Error: ${state.message}'));
          } else {
            return const Center(child: Text('No habits found'));
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddHabit(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildHabitsList(BuildContext context, List<Habit> habits) {
    if (habits.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.list, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'No habits yet',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Add your first habit to start tracking',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _navigateToAddHabit(context),
              child: const Text('Add Habit'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: habits.length,
      itemBuilder: (context, index) {
        final habit = habits[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(
              habit.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              'Created: ${habit.createdAt.toString().substring(0, 10)}',
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '+${habit.points}',
                  style: const TextStyle(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _navigateToEditHabit(context, habit),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _showDeleteConfirmation(context, habit),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _navigateToAddHabit(BuildContext context) async {
    final authState = context.read<AuthCubit>().state;
    if (authState is Authenticated) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AddEditHabitScreen(userId: authState.user.uid),
        ),
      );
    }
  }

  void _navigateToEditHabit(BuildContext context, Habit habit) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AddEditHabitScreen(userId: 'demo-user', habit: habit),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Habit habit) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Habit'),
            content: Text('Are you sure you want to delete "${habit.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<HabitsCubit>().deleteHabit(
                    'demo-user',
                    habit.id,
                  );
                },
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
