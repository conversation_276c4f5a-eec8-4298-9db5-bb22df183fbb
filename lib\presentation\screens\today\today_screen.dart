import 'package:adaa/core/constants/app_theme.dart';
import 'package:adaa/domain/entities/day.dart';
import 'package:adaa/domain/entities/habit.dart';
import 'package:adaa/presentation/blocs/auth/auth_cubit.dart';
import 'package:adaa/presentation/blocs/auth/auth_state.dart';
import 'package:adaa/presentation/blocs/daily_log/daily_log_cubit.dart';
import 'package:adaa/presentation/blocs/daily_log/daily_log_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class TodayScreen extends StatefulWidget {
  const TodayScreen({super.key});

  @override
  State<TodayScreen> createState() => _TodayScreenState();
}

class _TodayScreenState extends State<TodayScreen> {
  DateTime _selectedDate = DateTime.now();
  final TextEditingController _manualScoreController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadDailyLog();
  }

  @override
  void dispose() {
    _manualScoreController.dispose();
    super.dispose();
  }

  void _loadDailyLog() {
    final authState = context.read<AuthCubit>().state;
    if (authState is Authenticated) {
      context.read<DailyLogCubit>().loadDailyLog(
        authState.user.uid,
        _selectedDate,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Today'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () => _selectDate(context),
          ),
        ],
      ),
      body: BlocBuilder<DailyLogCubit, DailyLogState>(
        builder: (context, state) {
          if (state is DailyLogLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is DailyLogLoaded) {
            return _buildDailyLogContent(
              context,
              state.dailyLog,
              state.activeHabits,
              state.selectedDate,
            );
          } else if (state is DailyLogError) {
            return Center(child: Text('Error: ${state.message}'));
          } else {
            return const Center(child: Text('No data available'));
          }
        },
      ),
    );
  }

  Widget _buildDailyLogContent(
    BuildContext context,
    Day dailyLog,
    List<Habit> activeHabits,
    DateTime selectedDate,
  ) {
    final dateFormat = DateFormat('EEEE, MMMM d, y');
    final score = dailyLog.calculateScore();
    final percentage = score * 100;
    final formattedPercentage = percentage.toStringAsFixed(0);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            dateFormat.format(selectedDate),
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Today\'s Progress - $formattedPercentage% commitment',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    minHeight: 10,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getProgressColor(percentage),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Today\'s Habits',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildHabitsList(context, activeHabits, dailyLog),
          const SizedBox(height: 24),
          const Text(
            'Manual Score (max 3)',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildManualScoreInput(context, dailyLog),
        ],
      ),
    );
  }

  Widget _buildHabitsList(
    BuildContext context,
    List<Habit> habits,
    Day dailyLog,
  ) {
    if (habits.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'No active habits. Add some habits to track your progress!',
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Card(
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: habits.length,
        itemBuilder: (context, index) {
          final habit = habits[index];
          final isCompleted = dailyLog.completedHabits.contains(habit.id);

          return CheckboxListTile(
            title: Text(
              habit.name,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                decoration: isCompleted ? TextDecoration.lineThrough : null,
              ),
            ),
            subtitle: Text(
              'Created: ${habit.createdAt.toString().substring(0, 10)}',
            ),
            value: isCompleted,
            secondary: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isCompleted ? AppTheme.primaryGreen : Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '+${habit.points}',
                style: TextStyle(
                  color: isCompleted ? Colors.white : Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            onChanged:
                (value) => _toggleHabitCompletion(habit.id, value ?? false),
          );
        },
      ),
    );
  }

  Widget _buildManualScoreInput(BuildContext context, DailyLog dailyLog) {
    _manualScoreController.text = dailyLog.manualScore.toString();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Add a score for other tasks or achievements (0-3)'),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Slider(
                    value: dailyLog.manualScore,
                    min: 0,
                    max: 3,
                    divisions: 6,
                    label: dailyLog.manualScore.toString(),
                    onChanged: (value) {
                      _updateManualScore(value);
                    },
                  ),
                ),
                Container(
                  width: 60,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '+${dailyLog.manualScore}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadDailyLog();
    }
  }

  void _toggleHabitCompletion(String habitId, bool isCompleted) {
    final authState = context.read<AuthCubit>().state;
    if (authState is Authenticated) {
      if (isCompleted) {
        context.read<DailyLogCubit>().markHabitAsCompleted(
          authState.user.uid,
          habitId,
          _selectedDate,
        );
      } else {
        context.read<DailyLogCubit>().markHabitAsNotCompleted(
          authState.user.uid,
          habitId,
          _selectedDate,
        );
      }
    }
  }

  void _updateManualScore(double score) {
    final authState = context.read<AuthCubit>().state;
    if (authState is Authenticated) {
      context.read<DailyLogCubit>().updateManualScore(
        authState.user.uid,
        _selectedDate,
        score,
      );
    }
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 90) {
      return AppTheme.gradeA;
    } else if (percentage >= 80) {
      return AppTheme.gradeB;
    } else if (percentage >= 70) {
      return AppTheme.gradeC;
    } else if (percentage >= 60) {
      return AppTheme.gradeD;
    } else {
      return AppTheme.gradeF;
    }
  }
}
