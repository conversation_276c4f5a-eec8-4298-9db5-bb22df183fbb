import 'package:adaa/core/di/mock_injection_container.dart' as di;
import 'package:adaa/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Adaa Habit Tracker App Tests', () {
    setUpAll(() async {
      // Initialize dependency injection
      await di.init();
    });

    testWidgets('App should launch and show home screen', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify that the app launches without errors
      expect(find.byType(MaterialApp), findsOneWidget);

      // The app should show some content
      expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
    });

    testWidgets('App should build without errors', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify the app builds and renders
      expect(find.byType(MaterialApp), findsOneWidget);

      // Should have some UI elements
      expect(find.byType(Widget), findsAtLeastNWidgets(1));
    });
  });
}
